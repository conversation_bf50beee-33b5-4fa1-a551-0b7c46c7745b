enum Suit {
    Clubs,
    Diamonds,
    Hearts,
    Spades,
    YELLOW
}

class Card {
    suit: Suit;
    value: number;

    constructor(suit: Suit, value: number) {
        this.suit = suit;
        this.value = value;
    }

    hiLowValue() {
        if (this.value >= 10) {
            return -1;
        } else if (this.value <= 6) {
            return 1;
        } else {
            return 0;
        }
    }
}

class Deck {
    cards: Card[];

    constructor() {
        this.cards = [];

        for (let suit = 0; suit < 4; suit++) {
            for (let value = 1; value <= 13; value++) {
                this.cards.push(new Card(suit, value));
            }
        }
    }
}

class Shoe {
    cards: Card[];
    numDecks: number;
    penetration: number;
    requiresShuffle: boolean;
    runningCount: number;

    constructor(numDecks: number, penetration: number) {
        this.cards = [];
        this.numDecks = numDecks;
        this.penetration = Math.round(penetration * 13);
        this.requiresShuffle = true;
        this.runningCount = 0;

        for (let i = 0; i < numDecks; i++) {
            this.cards = this.cards.concat(new Deck().cards);
        }
    }

    getTrueCount(): number {
        const decksRemaining = this.cards.length / 52;
        if (decksRemaining < 0.5) return 0; // Avoid division by very small numbers
        return Math.round(this.runningCount / decksRemaining);
    }

    draw(): Card {
        const c = this.cards.pop();
        if (c!.suit == Suit.YELLOW) {
            this.requiresShuffle = true;
            return c!;
        }

        this.runningCount += c!.hiLowValue();

        return c!;
    }   

    /**
     * Enhanced shuffle method for better randomization.
     * Uses multiple shuffle passes and additional randomization techniques
     * to ensure a more random distribution of cards.
     */
    shuffle() {
        // Reset the true count when shuffling
        this.runningCount = 0;

        // Create a more robust random seed using multiple entropy sources
        const timeSeed = Date.now();
        const randomSeed = Math.random() * 1000000;
        let entropy = (timeSeed + randomSeed) % 1000000;

        // Custom random function with better entropy
        const customRandom = () => {
            entropy = (entropy * 9301 + 49297) % 233280;
            return entropy / 233280;
        };

        // Perform multiple shuffle passes for better randomization
        const shufflePasses = 5 + Math.floor(Math.random() * 5); // 5-9 passes

        for (let pass = 0; pass < shufflePasses; pass++) {
            // Enhanced Fisher-Yates shuffle
            for (let i = this.cards.length - 1; i > 0; i--) {
                // Use both built-in random and custom random for better distribution
                const rand1 = Math.random();
                const rand2 = customRandom();
                const combinedRandom = (rand1 + rand2) / 2;

                const j = Math.floor(combinedRandom * (i + 1));
                [this.cards[i], this.cards[j]] = [this.cards[j], this.cards[i]];
            }

            // Additional randomization techniques
            if (Math.random() < 0.7) {
                // Randomly reverse sections of the deck
                const sectionStart = Math.floor(Math.random() * (this.cards.length / 3));
                const sectionLength = Math.floor(Math.random() * (this.cards.length / 3));
                const sectionEnd = Math.min(sectionStart + sectionLength, this.cards.length - 1);
                this.reverseSection(sectionStart, sectionEnd);
            }

            if (Math.random() < 0.5) {
                // Randomly interleave two halves (riffle shuffle simulation)
                this.riffleShuffle();
            }
        }

        // Final randomization: multiple cuts
        for (let cuts = 0; cuts < 3; cuts++) {
            const cutPosition = Math.floor(Math.random() * this.cards.length);
            this.cards = [...this.cards.slice(cutPosition), ...this.cards.slice(0, cutPosition)];
        }

        // Insert the yellow card at the penetration point
        this.cards.splice(this.penetration, 0, new Card(Suit.YELLOW, 0));
        this.requiresShuffle = false;
    }

    /**
     * Helper method to reverse a section of the cards array
     * @param start Starting index
     * @param end Ending index
     */
    private reverseSection(start: number, end: number) {
        while (start < end) {
            [this.cards[start], this.cards[end]] = [this.cards[end], this.cards[start]];
            start++;
            end--;
        }
    }

    /**
     * Simulates a riffle shuffle by interleaving two halves of the deck
     */
    private riffleShuffle() {
        const midPoint = Math.floor(this.cards.length / 2);
        const leftHalf = this.cards.slice(0, midPoint);
        const rightHalf = this.cards.slice(midPoint);

        const shuffled: Card[] = [];
        let leftIndex = 0;
        let rightIndex = 0;

        // Randomly interleave cards from both halves
        while (leftIndex < leftHalf.length || rightIndex < rightHalf.length) {
            // Randomly choose which half to take from, with some bias toward alternating
            const takeFromLeft = leftIndex < leftHalf.length &&
                (rightIndex >= rightHalf.length || Math.random() < 0.5);

            if (takeFromLeft) {
                shuffled.push(leftHalf[leftIndex++]);
                // Sometimes take multiple cards from the same side (realistic riffle)
                if (Math.random() < 0.3 && leftIndex < leftHalf.length) {
                    shuffled.push(leftHalf[leftIndex++]);
                }
            } else {
                shuffled.push(rightHalf[rightIndex++]);
                // Sometimes take multiple cards from the same side (realistic riffle)
                if (Math.random() < 0.3 && rightIndex < rightHalf.length) {
                    shuffled.push(rightHalf[rightIndex++]);
                }
            }
        }

        this.cards = shuffled;
    }
}

class Hand {
    cards: Card[];
    doubled: boolean;
    bet: number;
    result: HandResult;

    constructor(bet: number) {
        this.bet = bet;
        this.cards = [];
        this.doubled = false;
        this.result = HandResult.READY;
    }

    /**
     * Calculates the total value of the hand according to Blackjack rules.
     * - Ace (value 1) = 1 or 11 (whichever is better)
     * - Face cards (values 11, 12, 13) = 10
     * - Number cards (values 2-10) = face value
     *
     * @returns The total value of the hand
     */
    getTotal(): number {
        let total = 0;
        let aces = 0;

        // First pass: count non-ace cards and count aces
        for (const card of this.cards) {
            if (card.value === 1) {
                aces++;
                total += 11; // Start by counting ace as 11
            } else if (card.value >= 11 && card.value <= 13) {
                total += 10; // Face cards are worth 10
            } else {
                total += card.value; // Number cards 2-10
            }
        }

        // Second pass: convert aces from 11 to 1 if total > 21
        while (total > 21 && aces > 0) {
            total -= 10; // Convert an ace from 11 to 1 (difference of 10)
            aces--;
        }

        return total;
    }

    /**
     * Determines if the hand is soft or hard according to Blackjack rules.
     * A soft hand contains at least one Ace that is being counted as 11.
     * A hard hand either contains no Aces, or all Aces are being counted as 1.
     *
     * @returns true if the hand is soft, false if the hand is hard
     */
    isSoft(): boolean {
        // Check if hand contains any Aces
        const aceCount = this.cards.filter(card => card.value === 1).length;

        if (aceCount === 0) {
            return false; // No Aces = definitely hard
        }

        // Calculate total using same logic as getTotal() to see if any Ace can be 11
        let total = 0;
        let acesRemaining = aceCount;

        // First pass: count non-ace cards and count aces as 11
        for (const card of this.cards) {
            if (card.value === 1) {
                total += 11; // Start by counting ace as 11
            } else if (card.value >= 11 && card.value <= 13) {
                total += 10; // Face cards are worth 10
            } else {
                total += card.value; // Number cards 2-10
            }
        }

        // Second pass: convert aces from 11 to 1 if total > 21
        while (total > 21 && acesRemaining > 0) {
            total -= 10; // Convert an ace from 11 to 1 (difference of 10)
            acesRemaining--;
        }

        // If we still have aces counted as 11 (acesRemaining < aceCount), it's soft
        return acesRemaining < aceCount;
    }

    /**
     * There is an assumption that doDouble and doSplit are checked before doHit is invoked.
     * 
     * @param dealerUpCard 
     * @returns 
     */
    doHit(dealerUpCard: Card) {
        const handValue = this.getTotal();

        if (this.isSoft()) { // Handle a soft hand
            if (handValue <= 17) {
                return true;
            } else if (handValue === 18) {
                return dealerUpCard.value >= 9 || dealerUpCard.value === 1;
            } else {
                return false; // Soft 19+ always stand
            }
        } else { // handle a hard hand or a hand with more than 2 cards
            if (handValue <= 11) {
                return true
            } else if (handValue === 12) {
                return dealerUpCard.value === 2 || dealerUpCard.value === 3 || dealerUpCard.value >= 7;
            } else if (handValue >= 13 && handValue <= 16) {
                return dealerUpCard.value >= 7;
            } else {
                return false;
            }
        }
    }

    doSplit(dealerUpCard: Card) {
        if (this.cards.length !== 2) {
            return false;
        }

        if (this.cards[0].value !== this.cards[1].value) {
            return false;
        }

        const cardValue = this.cards[0].value;
        if (cardValue === 1 || cardValue === 8) {
            return true;
        } else if (cardValue === 2 || cardValue === 3) {
            return dealerUpCard.value <= 7;
        } else if (cardValue === 4) {
            return dealerUpCard.value === 5 || dealerUpCard.value === 6;
        } else if (cardValue === 6) {
            return dealerUpCard.value <= 6;
        } else if (cardValue === 7) {
            return dealerUpCard.value <= 7;
        } else if (cardValue === 9) {
            return !(dealerUpCard.value >= 10 || dealerUpCard.value === 1 || dealerUpCard.value === 7);
        } else {
            return false;
        }
    }

    doSurrender(dealerUpCard: Card) {
        if (this.cards.length !== 2) {
            return false;
        }

        const cardValue = this.cards[0].value + this.cards[1].value;
        if (cardValue === 16) {
            return dealerUpCard.value === 9 || dealerUpCard.value >= 10 || dealerUpCard.value === 1;
        } else if (cardValue === 15) {
            return dealerUpCard.value >= 10;
        }

        return false;
    }

    doDouble(dealerUpCard: Card) {
        if (this.cards.length !== 2) {
            return false;
        }

        var handTotal = this.getTotal();
        if (this.isSoft()) { // Handle a soft hand
            if (handTotal === 13 || handTotal === 14) {
                return dealerUpCard.value === 5 || dealerUpCard.value === 6;
            } else if (handTotal === 15 || handTotal === 16) {
                return dealerUpCard.value >= 4 && dealerUpCard.value <= 6;
            } else if (handTotal === 17) {
                return dealerUpCard.value >= 3 && dealerUpCard.value <= 6;
            } else if (handTotal === 18) {
                return dealerUpCard.value >= 2 && dealerUpCard.value <= 6;
            } else if (handTotal === 19) {
                return dealerUpCard.value === 6;
            } else {
                return false;
            }
        } else { // handle a hard hand
            if (handTotal === 9) {
                return dealerUpCard.value >= 3 && dealerUpCard.value <= 6;
            } else if (handTotal === 10) {
                return dealerUpCard.value >= 2 && dealerUpCard.value <= 9;
            } else if (handTotal === 11) {
                return true;
            } else {
                return false;
            }
        }
    }
}

var deck = new Shoe(9, 1.5);
deck.shuffle();

const baseBid = 25;
const increment = 1;
let roundCount = 0;

let bank = 1000;
const handsPerHour = 100;

let currentBet = baseBid;

enum HandResult {
    READY,
    BUST,
    BLACKJACK,
    LOSE,
    SPLIT,
    SURRENDER,
    PUSH,
    WIN,
}

function processHand(hand: Hand, dealerUpCard: Card) : HandResult {
    if (hand.getTotal() === 21) {
        return HandResult.BLACKJACK;
    }

    if (hand.doSurrender(dealerUpCard)) {
        bank -= currentBet / 2;
        currentBet = baseBid;
        hand.result = HandResult.SURRENDER;
    } else if (hand.doDouble(dealerUpCard)) {
        bank -= currentBet;
        currentBet += currentBet;

        hand.doubled = true;
        hand.cards.push(deck.draw());

        hand.result = HandResult.READY;
    } else if (hand.doSplit(dealerUpCard)) {
        hand.result = HandResult.SPLIT;
    } else {
        while (hand.doHit(dealerUpCard)) {
            hand.cards.push(deck.draw());
        }

        if (hand.getTotal() > 21) {
            hand.result = HandResult.BUST;
        }
    }

    return hand.result;
}

console.log(`Starting with ${bank}`);

function playShoe() {
    const roundsAtStart = roundCount;
    deck = new Shoe(9, 1.5);
    deck.shuffle();

    // if (bank < baseBid) {
    //     console.log(`  Cannot play - bank (${bank}) is less than minimum bet (${baseBid})`);
    //     return;
    // }

    while (!deck.requiresShuffle) {
        let bid = baseBid;
        if (deck.getTrueCount() > 0) {
            bid += deck.getTrueCount() * baseBid;
        }

        let dealerHand = new Hand(0);
        let playerHands: Hand[] = [
            new Hand(bid),
        ];

        playerHands[0].cards.push(deck.draw());
        dealerHand.cards.push(deck.draw());
        playerHands[0].cards.push(deck.draw());
        dealerHand.cards.push(deck.draw());

        if (dealerHand.getTotal() === 21) { // Dealer Blackjack
            for (let i = 0; i < playerHands.length; i++) {
                const hand = playerHands[i];

                if (hand.getTotal() !== 21) {
                    bank -= hand.bet;
                    hand.result = HandResult.LOSE;
                } else {
                    hand.result = HandResult.PUSH;
                }
            }

            continue;
        }

        // Process player hands
        let i = 0;
        while (i < playerHands.length) {
            const hand = playerHands[i];

            while (hand.cards.length < 2) {
                hand.cards.push(deck.draw());
            }

            const result = processHand(hand, dealerHand.cards[1]);
            if (result === HandResult.BUST) {
                bank -= hand.bet;
            } else if (result === HandResult.BLACKJACK) {
                bank += (hand.bet * 1.5);
            } else if (result === HandResult.SURRENDER) {
                bank -= (hand.bet / 2);
            } else if (result === HandResult.SPLIT) {
                const newHand = new Hand(hand.bet);
                newHand.cards.push(hand.cards.pop()!);
                playerHands.push(newHand);
                
                hand.result = HandResult.READY;
                continue;
            } else if (result !== HandResult.READY) {
                throw 'Unexpected hand result';
            }

            i += 1;
        }

        // Process dealer hand
        if (playerHands.every(h => h.result === HandResult.BUST || h.result === HandResult.BLACKJACK)) {
            // Dealer does not draw when all hands are resolved.
            continue;
        }

        while (dealerHand.getTotal() < 17) { // assuming dealer stands on soft 17
            dealerHand.cards.push(deck.draw());
        }

        let roundNet = 0;

        if (dealerHand.getTotal() > 21) { // Dealer bust
            for (let i = 0; i < playerHands.length; i++) {
                const hand = playerHands[i];

                if (hand.result === HandResult.READY) {
                    bank += hand.bet;
                    roundNet += hand.bet;
                    hand.result = HandResult.WIN;
                } else if (hand.result === HandResult.BLACKJACK) {
                    // bank has already been adjusted
                    roundNet += (hand.bet * 1.5);
                } else if (hand.result === HandResult.BUST) {
                    // bank has already been adjusted
                    roundNet -= hand.bet;
                }
            }
        } else {
            for (let i = 0; i < playerHands.length; i++) {
                const hand = playerHands[i];

                if (hand.result === HandResult.READY) {
                    if (hand.getTotal() > dealerHand.getTotal()) {
                        bank += hand.bet;
                        roundNet += hand.bet;
                        hand.result = HandResult.WIN;
                    } else if (hand.getTotal() < dealerHand.getTotal()) {
                        bank -= hand.bet;
                        roundNet -= hand.bet;
                        hand.result = HandResult.LOSE;
                    } else {
                        hand.result = HandResult.PUSH;
                    }
                } else if (hand.result === HandResult.BLACKJACK) {
                    // bank has already been adjusted
                    roundNet += (hand.bet * 1.5);
                } else if (hand.result === HandResult.BUST) {
                    // bank has already been adjusted
                    roundNet -= hand.bet;
                }
            }
        }

        // Remove progressive betting - we're using card counting instead
        // The bet is already determined by true count in the main loop

        roundCount += 1;
    }

    const roundsThisShoe = roundCount - roundsAtStart;
    console.log(`  Played ${roundsThisShoe} rounds in this shoe`);
}

let shoeNumber = 1;

for (let i = 0; i < 1000; i++) {
    playShoe();
    console.log(`Shoe ${shoeNumber++}: Ended with ${bank} after ${roundCount} total rounds`);
}
