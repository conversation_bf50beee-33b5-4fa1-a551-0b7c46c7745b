enum Suit {
    Clubs,
    Diamonds,
    Hearts,
    Spades,
    YELLOW
}

class Card {
    suit: Suit;
    value: number;

    constructor(suit: Suit, value: number) {
        this.suit = suit;
        this.value = value;
    }

    trueCount() {
        if (this.value >= 10) {
            return -1;
        } else if (this.value <= 6) {
            return 1;
        } else {
            return 0;
        }
    }
}

class Deck {
    cards: Card[];

    constructor() {
        this.cards = [];

        for (let suit = 0; suit < 4; suit++) {
            for (let value = 1; value <= 13; value++) {
                this.cards.push(new Card(suit, value));
            }
        }
    }
}

class Shoe {
    cards: Card[];
    numDecks: number;
    penetration: number;
    requiresShuffle: boolean;
    trueCount: number;

    constructor(numDecks: number, penetration: number) {
        this.cards = [];
        this.numDecks = numDecks;
        this.penetration = Math.round(penetration * 13);
        this.requiresShuffle = true;
        this.trueCount = 0;

        for (let i = 0; i < numDecks; i++) {
            this.cards = this.cards.concat(new Deck().cards);
        }
    }

    draw(): Card {
        if (this.requiresShuffle) {
            throw 'Shuffle is required';
        }

        const c = this.cards.pop();
        if (c!.suit == Suit.YELLOW) {
            this.requiresShuffle = true;
            return c!;
        }

        this.trueCount += c!.trueCount();

        return c!;
    }   

    shuffle() {
        for (let i = this.cards.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.cards[i], this.cards[j]] = [this.cards[j], this.cards[i]];
        }

        this.cards.splice(this.penetration, 0, new Card(Suit.YELLOW, 0));
        this.requiresShuffle = false;
    }
}

class Hand {
    cards: Card[];
    doubled: boolean;
    bet: number;
    result: HandResult;

    constructor(bet: number) {
        this.bet = bet;
        this.cards = [];
        this.doubled = false;
        this.result = HandResult.READY;
    }

    /**
     * Calculates the total value of the hand according to Blackjack rules.
     * - Ace (value 1) = 1 or 11 (whichever is better)
     * - Face cards (values 11, 12, 13) = 10
     * - Number cards (values 2-10) = face value
     *
     * @returns The total value of the hand
     */
    getTotal(): number {
        let total = 0;
        let aces = 0;

        // First pass: count non-ace cards and count aces
        for (const card of this.cards) {
            if (card.value === 1) {
                aces++;
                total += 11; // Start by counting ace as 11
            } else if (card.value >= 11 && card.value <= 13) {
                total += 10; // Face cards are worth 10
            } else {
                total += card.value; // Number cards 2-10
            }
        }

        // Second pass: convert aces from 11 to 1 if total > 21
        while (total > 21 && aces > 0) {
            total -= 10; // Convert an ace from 11 to 1 (difference of 10)
            aces--;
        }

        return total;
    }

    /**
     * There is an assumption that doDouble and doSplit are checked before doHit is invoked.
     * 
     * @param dealerUpCard 
     * @returns 
     */
    doHit(dealerUpCard: Card) {

        while (true) {
            const handValue = this.cards.reduce((arr, c) => arr + c.value, 0);

            if (this.cards.length === 2 && this.cards.some(c => c.value === 1)) { // Handle a soft hand
                var otherCardValue = this.cards.find(c => c.value !== 1)!.value;
                
                if (otherCardValue >= 2 && otherCardValue <= 6) {
                    return true;
                } else if (otherCardValue === 7) {
                    return dealerUpCard.value >= 9 || dealerUpCard.value === 1;
                } else {
                    return false;
                }
            } else { // handle a hard hand or a hand with more than 2 cards
                const cardValue = this.cards[0].value + this.cards[1].value;

                if (cardValue === 9) {
                    return dealerUpCard.value >= 3 && dealerUpCard.value <= 6;
                } else if (cardValue === 10) {
                    return dealerUpCard.value >= 2 && dealerUpCard.value <= 9;
                } else if (cardValue === 11) {
                    return true;
                } else {
                    return false;
                }
            }

            // this.cards.push(deck.draw());
        }
    }

    doSplit(dealerUpCard: Card) {
        if (this.cards.length !== 2) {
            return false;
        }

        if (this.cards[0].value !== this.cards[1].value) {
            return false;
        }

        const cardValue = this.cards[0].value;
        if (cardValue === 1 || cardValue === 8) {
            return true;
        } else if (cardValue === 2 || cardValue === 3) {
            return dealerUpCard.value <= 7;
        } else if (cardValue === 4) {
            return dealerUpCard.value === 5 || dealerUpCard.value === 6;
        } else if (cardValue === 6) {
            return dealerUpCard.value <= 6;
        } else if (cardValue === 7) {
            return dealerUpCard.value <= 7;
        } else if (cardValue === 9) {
            return !(dealerUpCard.value === 10 || dealerUpCard.value === 1 || dealerUpCard.value === 7);
        } else {
            return false;
        }
    }

    doSurrender(dealerUpCard: Card) {
        if (this.cards.length !== 2) {
            return false;
        }

        const cardValue = this.cards[0].value + this.cards[1].value;
        if (cardValue === 16) {
            return dealerUpCard.value === 9 || dealerUpCard.value === 10 || dealerUpCard.value === 1;
        } else if (cardValue === 15) {
            return dealerUpCard.value === 10;
        }

        return false;
    }

    doDouble(dealerUpCard: Card) {
        if (this.cards.length !== 2) {
            return false;
        }

        if (this.cards.some(c => c.value === 1)) { // Handle a soft hand
            var otherCardValue = this.cards.find(c => c.value !== 1)!.value;
            
            if (otherCardValue === 2 || otherCardValue === 3) {
                return dealerUpCard.value === 5 || dealerUpCard.value === 6;
            } else if (otherCardValue === 4 || otherCardValue === 5) {
                return dealerUpCard.value >= 4 && dealerUpCard.value <= 6;
            } else if (otherCardValue === 6) {
                return dealerUpCard.value >= 3 && dealerUpCard.value <= 6;
            } else if (otherCardValue === 7) {
                return dealerUpCard.value >= 2 && dealerUpCard.value <= 6;
            } else if (otherCardValue === 8) {
                return dealerUpCard.value === 6;
            } else {
                return false;
            }
        } else { // handle a hard hand
            const cardValue = this.cards[0].value + this.cards[1].value;

            if (cardValue === 9) {
                return dealerUpCard.value >= 3 && dealerUpCard.value <= 6;
            } else if (cardValue === 10) {
                return dealerUpCard.value >= 2 && dealerUpCard.value <= 9;
            } else if (cardValue === 11) {
                return true;
            } else {
                return false;
            }
        }
    }
}

var deck = new Shoe(9, 1.5);
deck.shuffle();

const baseBid = 25;
const increment = 5;

let bank = 1000;
const handsPerHour = 100;

let currentBet = baseBid;

enum HandResult {
    READY,
    BUST,
    BLACKJACK,
    LOSE,
    SPLIT,
    SURRENDER,
}

function processHand(hand: Hand, dealerUpCard: Card) : HandResult {
    if (hand.cards[0].value + hand.cards[1].value === 21) {
        return HandResult.BLACKJACK;
    }

    if (hand.doSurrender(dealerUpCard)) {
        bank -= currentBet / 2;
        currentBet = baseBid;
        return HandResult.SURRENDER;
    } else if (hand.doDouble(dealerUpCard)) {
        bank -= currentBet;
        currentBet += currentBet;

        hand.doubled = true;
        hand.cards.push(deck.draw());

        return HandResult.READY;
    } else if (hand.doSplit(dealerUpCard)) {
        return HandResult.SPLIT;
    }
}

while (bank >= baseBid) {
    let dealerHand = new Hand(0);
    let playerHands: Hand[] = [
        new Hand(currentBet),
    ];

    playerHands[0].cards.push(deck.draw());
    dealerHand.cards.push(deck.draw());
    playerHands[0].cards.push(deck.draw());
    dealerHand.cards.push(deck.draw());

    if (dealerHand.cards[0].value + dealerHand.cards[1].value === 21) {
        const amountLost = playerHands.reduce((acc, hand) => acc + hand.bet, 0);
        bank -= amountLost;
        continue;
    }

    let i = 0;
    while (i < playerHands.length) {
        const hand = playerHands[i];

        while (hand.cards.length < 2) {
            hand.cards.push(deck.draw());
        }

        const result = processHand(hand, dealerHand.cards[1]);
        if (result === HandResult.BUST) {
            bank -= hand.bet;
        } else if (result === HandResult.BLACKJACK) {
            bank += hand.bet * 1.5;
        } else if (result === HandResult.SURRENDER) {
            bank -= hand.bet / 2;
        } else if (result === HandResult.SPLIT) {
            const newHand = new Hand(hand.bet);
            newHand.cards.push(hand.cards.pop()!);
            playerHands.push(newHand);

            continue;
        } else if (result !== HandResult.READY) {
            throw 'Unexpected hand result';
        }

        i += 1;
    }


}
